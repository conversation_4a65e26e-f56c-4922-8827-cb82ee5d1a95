'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useRef,
  ReactNode,
} from 'react';
import '@/lib/amplify';
import { AuthSession, getCurrentUser, signOut } from 'aws-amplify/auth';
import { fetchAuthSession } from 'aws-amplify/auth';
import { UserDetails } from '@/services/userService';

interface AuthContextType {
  isAuthenticated: boolean;
  isAdmin: boolean;
  loading: boolean;
  userDetails: UserDetails | null;
  accessToken: string | null;
  idToken: string | null;
  logout: () => void;
  checkAuth: () => Promise<UserDetails | null>;
  fetchUserDetails: () => Promise<UserDetails | null>;
  getTokens: (
    forceRefresh?: boolean,
  ) => Promise<{ accessToken: string | null; idToken: string | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [idToken, setIdToken] = useState<string | null>(null);
  const hasInitialized = useRef<boolean>(false);

  const checkIsAdmin = (session: AuthSession): boolean => {
    try {
      const accessToken = session.tokens?.accessToken;
      if (!accessToken) return false;

      const payload = accessToken.payload;
      const scope = payload?.['cognito:groups'] as Array<string> | undefined;

      return scope?.includes('admin') || false;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  };

  const getTokens = useCallback(
    async (
      forceRefresh: boolean = true,
    ): Promise<{ accessToken: string | null; idToken: string | null }> => {
      try {
        const session = await fetchAuthSession({ forceRefresh });
        const newAccessToken = session.tokens?.accessToken?.toString() || null;
        const newIdToken = session.tokens?.idToken?.toString() || null;

        console.log('Current user access token:', newAccessToken);
        console.log('Current user id token:', newIdToken);

        // Update stored tokens
        setAccessToken(newAccessToken);
        setIdToken(newIdToken);

        return { accessToken: newAccessToken, idToken: newIdToken };
      } catch (error) {
        console.error('Error getting tokens:', error);
        setAccessToken(null);
        setIdToken(null);
        return { accessToken: null, idToken: null };
      }
    },
    [],
  );

  // Function to fetch user details from the API
  const fetchUserDetails = async (): Promise<UserDetails | null> => {
    try {
      const { accessToken: token, idToken } = await getTokens();
      if (!token || !idToken) {
        return null;
      }
      const details = await getCurrentUser();

      const session = await fetchAuthSession({ forceRefresh: false });
      const isAdminUser = checkIsAdmin(session);

      return {
        uid: details.userId,
        email: details.signInDetails?.loginId || '',
        isAdmin: isAdminUser,
        role: isAdminUser ? 'admin' : 'user',
      };
    } catch (error) {
      console.error('Error fetching user details:', error);
      return null;
    }
  };

  const checkAuth = useCallback(async (): Promise<UserDetails | null> => {
    setLoading(true);

    try {
      // Get tokens and session
      const { accessToken: token } = await getTokens(false);

      if (token) {
        try {
          const currentUser = await getCurrentUser();
          console.log('Auth check - Current user:', currentUser);

          // Get session for admin check
          const session = await fetchAuthSession({ forceRefresh: false });
          const isAdminUser = checkIsAdmin(session);

          const userDetails = {
            uid: currentUser.userId,
            email: currentUser.signInDetails?.loginId || '',
            isAdmin: isAdminUser,
            role: isAdminUser ? 'admin' : 'user',
          };

          setUserDetails(userDetails);
          setIsAuthenticated(true);
          setIsAdmin(userDetails.isAdmin);

          return userDetails;
        } catch (userError) {
          console.error('Error getting current user:', userError);
          setIsAuthenticated(false);
          setIsAdmin(false);
          setUserDetails(null);
          setAccessToken(null);
          setIdToken(null);
          return null;
        }
      } else {
        console.log('No active session found');
        setIsAuthenticated(false);
        setIsAdmin(false);
        setUserDetails(null);
        setAccessToken(null);
        setIdToken(null);
        return null;
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserDetails(null);
      setAccessToken(null);
      setIdToken(null);
      return null;
    } finally {
      setLoading(false);
    }
  }, [getTokens]); // Include getTokens dependency

  // Logout function
  const logout = async () => {
    try {
      await signOut({ global: true });
      // Clear all auth state
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserDetails(null);
      setAccessToken(null);
      setIdToken(null);
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  // Initialize auth only once on mount
  useEffect(() => {
    let isMounted = true;

    const initAuth = async () => {
      if (hasInitialized.current) return;
      hasInitialized.current = true;

      try {
        console.log('AuthContext - Initializing auth...');
        await checkAuth();
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (isMounted) {
          setIsAuthenticated(false);
          setIsAdmin(false);
          setUserDetails(null);
          setLoading(false);
        }
      }
    };

    if (typeof window !== 'undefined') {
      initAuth();
    }

    return () => {
      isMounted = false;
    };
  }, [checkAuth]); // Include checkAuth but prevent re-runs with hasInitialized

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isAdmin,
        loading,
        userDetails,
        accessToken,
        idToken,
        logout,
        checkAuth,
        fetchUserDetails,
        getTokens,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
