'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  Check,
  Clock,
  MoreHorizontal,
  Plus,
  Search,
  Shield,
  ShieldAlert,
  Trash2,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { InviteStaffDialog } from '@/components/blocks/invite-staff-dialog';
// import { StaffProfileDialog } from '@/components/blocks/staff-profile-dialog';
import { useAuth } from '@/contexts/AuthContext';
import { useClinic } from '@/contexts/ClinicContext';
import { getStaffMembers } from '@/services/userService';
import { StaffMember } from '@/lib/types';
import { toast } from 'sonner';
import { ChangeRoleDialog } from '@/components/blocks/change-role-dialog';
import { RemoveStaffDialog } from '@/components/blocks/remove-staff-dialog';

export default function StaffManagementPage() {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [staffList, setStaffList] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(false);
  const [accessToken, setAccessToken] = useState<string>('');
  const [idToken, setIdToken] = useState<string>('');

  const { getTokens } = useAuth();
  const { selectedClinic, clinics } = useClinic();

  const fetchStaffMembers = useCallback(async () => {
    if (!selectedClinic?._id) return;

    setLoading(true);
    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !idToken) {
        toast.error('Authentication required');
        return;
      }

      setIdToken(idToken);
      setAccessToken(accessToken);

      const staffData = await getStaffMembers(selectedClinic._id, accessToken);
      setStaffList(staffData);
    } catch (error) {
      console.error('Error fetching staff members:', error);
      toast.error('Failed to fetch staff members');
    } finally {
      setLoading(false);
    }
  }, [selectedClinic?._id, getTokens]);

  useEffect(() => {
    if (selectedClinic?._id) {
      fetchStaffMembers();
    }
  }, [selectedClinic?._id, fetchStaffMembers]);

  const filteredStaff = staffList.filter((staff) => {
    const fullName =
      `${staff.user_details.first_name} ${staff.user_details.last_name}`.toLowerCase();
    const query = searchQuery.toLowerCase();
    return (
      fullName.includes(query) ||
      staff.user_details.email.toLowerCase().includes(query) ||
      (staff.mapping.role && staff.mapping.role.toLowerCase().includes(query))
    );
  });

  const handleRoleChange = async (staffId: string, newRole: string) => {
    setStaffList((prevStaff) =>
      prevStaff.map((staff) =>
        staff.user_details._id === staffId
          ? { ...staff, mapping: { ...staff.mapping, role: newRole } }
          : staff,
      ),
    );
    await fetchStaffMembers();
  };

  const handleRemove = async (staffId: string) => {
    setStaffList((prevStaff) =>
      prevStaff.filter((staff) => staff.user_details._id !== staffId),
    );
    await fetchStaffMembers();
  };

  const getDisplayName = (staff: StaffMember) => {
    return (
      `${staff.user_details.first_name} ${staff.user_details.last_name}`.trim() ||
      staff.user_details.email
    );
  };

  const getInitials = (staff: StaffMember) => {
    const firstName = staff.user_details.first_name || '';
    const lastName = staff.user_details.last_name || '';
    return (
      `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() ||
      staff.user_details.email?.charAt(0).toUpperCase()
    );
  };

  const getStaffStatus = (staff: StaffMember): 'active' | 'pending' => {
    return staff.mapping.status || 'active';
  };

  const formatJoinDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Staff Management
          </h1>
          {selectedClinic && (
            <p className="text-sm text-muted-foreground mt-1">
              Managing staff for {selectedClinic.clinic_name}
            </p>
          )}
        </div>
        <div className="mt-2 sm:mt-0 flex gap-2">
          <Button
            variant="outline"
            onClick={fetchStaffMembers}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Invite Staff
          </Button>
        </div>
      </div>

      {!selectedClinic ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <p className="text-muted-foreground">
                Please select a clinic to view staff members.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card design={false}>
          <CardHeader>
            <CardTitle>Staff Members</CardTitle>
            <CardDescription>
              Manage your clinic staff and their access levels
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex w-full max-w-sm items-center space-x-2">
                <Input
                  placeholder="Search staff..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-9"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 px-4 shrink-0"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Joined</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          Loading staff members...
                        </TableCell>
                      </TableRow>
                    ) : filteredStaff.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8">
                          No staff members found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredStaff.map((staff) => (
                        <TableRow key={staff.user_details._id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage
                                  src={`https://xvatar.vercel.app/api/avatar/${staff.user_details.email}.svg?rounded=120&size=240`}
                                />
                                <AvatarFallback>
                                  {getInitials(staff)}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div>{getDisplayName(staff)}</div>
                                <div className="text-sm text-muted-foreground">
                                  {staff.user_details.email || ''}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {staff.mapping.role === 'master' ? (
                                <ShieldAlert className="size-5 text-rose-500" />
                              ) : (
                                <Shield className="size-5 text-blue-500" />
                              )}
                              {staff.mapping.role === 'master'
                                ? 'Master'
                                : 'Staff'}
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStaffStatus(staff) === 'active' ? (
                              <Badge variant="success" className="px-4 py-1.5">
                                <Check className="mr-1 h-3 w-3" /> Active
                              </Badge>
                            ) : (
                              <Badge variant="pending" className="px-4 py-1.5">
                                <Clock className="mr-1 h-3 w-3" /> Pending
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {formatJoinDate(staff.user_details.created_at)}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Open menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                {/* <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedStaff(staff);
                                    setIsProfileDialogOpen(true);
                                  }}
                                >
                                  <User className="mr-2 h-4 w-4" />
                                  <span>View Profile</span>
                                </DropdownMenuItem> */}
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedStaff(staff);
                                    setIsRoleDialogOpen(true);
                                  }}
                                >
                                  <Shield className="mr-2 h-4 w-4" />
                                  <span>Change Role</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                {getStaffStatus(staff) === 'pending' ? (
                                  <DropdownMenuItem>
                                    <Clock className="mr-2 h-4 w-4" />
                                    <span>Resend Invitation</span>
                                  </DropdownMenuItem>
                                ) : null}
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => {
                                    setSelectedStaff(staff);
                                    setIsRemoveDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  <span>Remove</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <InviteStaffDialog
        open={isInviteDialogOpen}
        onOpenChange={setIsInviteDialogOpen}
        clinics={clinics}
      />

      {/* <StaffProfileDialog
        open={isProfileDialogOpen}
        onOpenChange={setIsProfileDialogOpen}
        staff={
          selectedStaff
            ? {
                id: parseInt(selectedStaff.user_details._id.slice(-8), 16),
                name: getDisplayName(selectedStaff),
                email: selectedStaff.user_details.email,
                role: selectedStaff.mapping.role || 'Standard',
                status: getStaffStatus(selectedStaff),
                joinedDate: formatJoinDate(
                  selectedStaff.user_details.created_at,
                ),
              }
            : null
        }
      /> */}

      {selectedStaff && (
        <ChangeRoleDialog
          open={isRoleDialogOpen}
          onOpenChange={setIsRoleDialogOpen}
          staff={{
            id: parseInt(selectedStaff.user_details.uid.slice(-8), 16),
            name: getDisplayName(selectedStaff),
            currentRole:
              (selectedStaff.mapping.role as 'master' | 'staff') || 'staff',
          }}
          onRoleChange={async (newRole) => {
            await handleRoleChange(selectedStaff.user_details.uid, newRole);
          }}
          userId={selectedStaff.user_details.uid}
          clinicId={selectedClinic?._id || ''}
          xidToken={idToken}
          accessToken={accessToken}
        />
      )}

      {selectedStaff && (
        <RemoveStaffDialog
          open={isRemoveDialogOpen}
          onOpenChange={setIsRemoveDialogOpen}
          staff={{
            id: parseInt(selectedStaff.user_details.uid.slice(-8), 16),
            name: getDisplayName(selectedStaff),
            currentRole:
              (selectedStaff.mapping.role as 'master' | 'staff') || 'staff',
          }}
          onRemove={async () => {
            await handleRemove(selectedStaff.user_details.uid);
          }}
          userId={selectedStaff.user_details.uid}
          clinicId={selectedClinic?._id || ''}
          accessToken={accessToken}
          xidToken={idToken}
        />
      )}
    </div>
  );
}
