'use client';

import { API_ENDPOINTS } from '@/actions';
import { ClinicType } from '@/lib/types';

export interface ClinicCreationData {
  clinic_name: string;
  clinic_email: string;
  clinic_phone?: string;
  clinic_website: string;
}

export interface ClinicCreationResponse {
  data: ClinicType;
}

/**
 * Fetches user clinics from the backend
 * @param accessToken Cognito access token
 * @returns Promise with the user clinics response
 */
export async function getUserClinics(accessToken: string): Promise<{
  ok: boolean;
  status: number;
  data?: ClinicType[];
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.CLINICS, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to fetch clinics',
      };
    }

    const data = await response.json();
    return {
      ok: true,
      status: response.status,
      data: data.data || [],
    };
  } catch (error) {
    console.error('Error fetching clinics:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while fetching clinics',
    };
  }
}

/**
 * Creates a new clinic in the backend
 * @param clinicData Clinic data containing required fields
 * @param accessToken Cognito access token
 * @returns Promise with the clinic creation response
 */
export async function createClinic(
  clinicData: ClinicCreationData,
  accessToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: ClinicCreationResponse;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.CLINICS, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(clinicData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to create clinic',
      };
    }

    const data = await response.json();
    return {
      ok: true,
      status: response.status,
      data,
    };
  } catch (error) {
    console.error('Error creating clinic:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while creating clinic',
    };
  }
}

export interface ClinicUpdateData extends ClinicCreationData {
  clinic_addresses?: Array<{
    address?: string;
    full_address: string;
    business_location_id?: string;
  }>;
  human_transfer_destination_number?: string;
  diagnostic_services?: Array<{
    name: string;
    is_referral_required: boolean;
  }>;
}

/**
 * Updates an existing clinic
 * @param clinicId Clinic ID to update
 * @param clinicData Updated clinic data
 * @param accessToken Cognito access token
 * @returns Promise with the clinic update response
 */
export async function updateClinic(
  clinicId: string,
  clinicData: Partial<ClinicUpdateData>,
  accessToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: ClinicType;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.CLINICS, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        ...clinicData,
        _id: clinicId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.error?.message || 'Failed to update clinic',
      };
    }

    const data = await response.json();
    return {
      ok: true,
      status: response.status,
      data: data.data,
    };
  } catch (error) {
    console.error('Error updating clinic:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while updating clinic',
    };
  }
}

/**
 * Deactivates a clinic (soft delete)
 * @param clinicId Clinic ID to deactivate
 * @param accessToken Cognito access token
 * @returns Promise with the deactivation response
 */
export async function deactivateClinic(
  clinicId: string,
  accessToken: string,
): Promise<{
  ok: boolean;
  status: number;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_ENDPOINTS.CLINICS}/${clinicId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to deactivate clinic',
      };
    }

    return {
      ok: true,
      status: response.status,
    };
  } catch (error) {
    console.error('Error deactivating clinic:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while deactivating clinic',
    };
  }
}

/**
 * Fetches a single clinic by ID
 * @param clinicId Clinic ID to fetch
 * @param accessToken Cognito access token
 * @returns Promise with the clinic data
 */
export async function getClinicById(
  clinicId: string,
  accessToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: ClinicType;
  error?: string;
}> {
  try {
    const response = await fetch(`${API_ENDPOINTS.CLINICS}/${clinicId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to fetch clinic',
      };
    }

    const data = await response.json();
    return {
      ok: true,
      status: response.status,
      data: data.data,
    };
  } catch (error) {
    console.error('Error fetching clinic:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while fetching clinic',
    };
  }
}
