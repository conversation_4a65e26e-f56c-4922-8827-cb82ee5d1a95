'use client';

import { useState } from 'react';
import { <PERSON>r<PERSON><PERSON>, Loader2 } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import {
  registerAdmin,
  AdminPrivilegeLevel,
  RegisterAdminPayload,
} from '@/actions/admin';
import { useAuth } from '@/contexts/AuthContext';

interface AddAdminDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAdminAdded: () => void;
}

export function AddAdminDialog({
  open,
  onOpenChange,
  onAdminAdded,
}: AddAdminDialogProps) {
  const [uid, setUid] = useState('');
  const [privilegeLevel, setPrivilegeLevel] = useState<AdminPrivilegeLevel>(
    AdminPrivilegeLevel.VIEWER,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { getTokens } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!uid.trim()) {
      toast.error('Please enter a UID');
      return;
    }

    if (!privilegeLevel) {
      toast.error('Please select a privilege level');
      return;
    }

    setIsSubmitting(true);

    try {
      const { accessToken, idToken } = await getTokens();

      if (!accessToken || !idToken) {
        toast.error('Authentication required');
        return;
      }

      const payload: RegisterAdminPayload = {
        uid: uid.trim(),
        privilege_level: privilegeLevel,
      };

      const result = await registerAdmin(payload, accessToken, idToken);

      if (result.ok) {
        toast.success('Admin added successfully');
        setUid('');
        setPrivilegeLevel(AdminPrivilegeLevel.VIEWER);
        onAdminAdded();
        onOpenChange(false);
      } else {
        toast.error(result.error || 'Failed to add admin');
      }
    } catch (error) {
      console.error('Error adding admin:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setUid('');
      setPrivilegeLevel(AdminPrivilegeLevel.VIEWER);
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Add New Admin
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="uid">User ID (UID)</Label>
            <Input
              id="uid"
              type="text"
              placeholder="Enter user UID (e.g., 898e3418-1041-70a9-35c7-3fad3d4c737a)"
              value={uid}
              onChange={(e) => setUid(e.target.value)}
              disabled={isSubmitting}
              required
            />
            <p className="text-sm text-muted-foreground">
              Enter the unique identifier for the user you want to make an
              admin.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="privilege-level">Privilege Level</Label>
            <Select
              value={privilegeLevel}
              onValueChange={(value) =>
                setPrivilegeLevel(value as AdminPrivilegeLevel)
              }
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select privilege level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={AdminPrivilegeLevel.VIEWER}>
                  Viewer - Read-only access
                </SelectItem>
                <SelectItem value={AdminPrivilegeLevel.EDITOR}>
                  Editor - Can modify content
                </SelectItem>
                <SelectItem value={AdminPrivilegeLevel.OWNER}>
                  Owner - Full administrative access
                </SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              Choose the level of administrative access for this user.
            </p>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Admin
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
