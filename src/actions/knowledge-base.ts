'use server';

import { KnowledgeBaseApiResponse, KnowledgeBaseType } from '@/lib/types';
import { API_ENDPOINTS, QueryParams } from '.';

export async function getKnowledgeBases(
  clinicId: string,
  accessToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: KnowledgeBaseApiResponse;
  error?: {
    message: string;
  };
}> {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(
      `${API_ENDPOINTS.RETELL_KNOWLEDGE_BASES}?clinic_id=${clinicId}`,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          // 'x-id-token': idToken,
        },
        next: {
          revalidate: 600,
          tags: [`knowledge-bases-${clinicId}`],
        },
      },
    );

    const data: KnowledgeBaseApiResponse = await response.json();

    return { ok: response.ok, status: response.status, data };
  } catch {
    return {
      ok: false,
      status: 500,
      error: { message: 'Failed to fetch knowledge bases. Please try again.' },
    };
  }
}

export async function uploadKnowledgeBaseFiles(
  clinicId: string,
  token: string,
  files: FormData,
) {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: files,
    });

    const responseData = (await response.json()) as KnowledgeBaseType;

    return { ok: response.ok, status: response.status, data: responseData };
  } catch {
    return {
      ok: false,
      status: 500,
      error: 'Failed to upload files to knowledge base. Please try again.',
    };
  }
}

export async function createKnowledgeBaseWithFiles(
  clinicId: string,
  files: File[],
  accessToken: string,
  idToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: KnowledgeBaseType;
  error?: {
    message: string;
  };
}> {
  try {
    if (!accessToken) {
      return {
        ok: false,
        status: 401,
        error: { message: 'Access token is not available' },
      };
    }

    if (!clinicId) {
      return {
        ok: false,
        status: 400,
        error: { message: 'Clinic ID is required' },
      };
    }

    if (files.length === 0) {
      return {
        ok: false,
        status: 400,
        error: { message: 'Please select files to upload' },
      };
    }

    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    const url = new URL(API_ENDPOINTS.RETELL_KNOWLEDGE_BASES);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);
    const urlString = url.toString();
    const response = await fetch(urlString, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'x-id-token': idToken,
      },
      body: formData,
    });

    const responseData = await response.json();
    console.log('Response:', responseData);
    return { ok: response.ok, status: response.status, data: responseData };
  } catch (err) {
    console.error('Error creating knowledge base with files:', err);
    return {
      ok: false,
      status: 500,
      error: { message: 'An unexpected error occurred' },
    };
  }
}
