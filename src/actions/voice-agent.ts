'use client';

import {
  VoiceAgent,
  ApiResponse,
  CreateVoiceAgentInput,
} from '@/lib/agent-types';
import { API_ENDPOINTS, QueryParams } from '.';

export async function getVoiceAgent(
  clinicId: string,
  accessToken: string,
  idToken: string,
): Promise<ApiResponse<VoiceAgent>> {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_GET_AGENT);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'x-id-token': idToken,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.log('Error:', errorData.error);
      return {
        ok: false,
        error: errorData.error.message || 'Failed to fetch voice agent data',
      };
    }
    const data = (await response.json()) as VoiceAgent;
    return {
      ok: true,
      data,
    };
  } catch {
    return {
      ok: false,
      error: 'An unexpected error occurred',
    };
  }
}

export async function createVoiceAgent(
  clinicId: string,
  data: CreateVoiceAgentInput,
  accessToken: string,
  idToken: string,
): Promise<ApiResponse<VoiceAgent>> {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_AGENT);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'x-id-token': idToken,
      },
      body: JSON.stringify(data),
    });
    console.log('Response:', response);
    if (!response.ok) {
      const errorData = await response.json();
      console.log('Error:', errorData.error);
      return {
        ok: false,
        error: errorData.error.message || 'Failed to create voice agent',
      };
    }

    const result = await response.json();
    return {
      ok: true,
      data: result.data,
    };
  } catch {
    return {
      ok: false,
      error: 'An unexpected error occurred',
    };
  }
}
